# WhatsApp Business API Configuration
WHATSAPP_VERIFY_TOKEN=WeBhoOk
WHATSAPP_ACCESS_TOKEN=EAAFbMv2U8kYBO3Tky9kHY7aNlrTwsKZA7JoA3yZAzUYAmOnXsMZB8nT3m68a9WRhlmwyClWhW02oF8jq0ZCBDpBDOEGibgQZCowNWMz6YvQFPv87daMLFlkEm7uXRvheGR0lkdCf4f2j0vza0ZAZBKYLKvC2B30QUM2cZCkyVPgCbneWj4CO2wR7M0CWI2rCCEUW9JZASCF9YCcZADtuvobUwqrGpmJnmoG7Ausy9rm3hO2Uc9w4amg6gUOwKWR0ZAk
WHATSAPP_PHONE_NUMBER_ID=394218977097517
WHATSAPP_API_VERSION=v18.0
WHATSAPP_WEBHOOK_SECRET=WeBhoOk
WHATSAPP_BUSINESS_ID=354803684384472

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=meta.llama3-8b-instruct-v1:0
BEDROCK_AGENT_ID=R1FMKXHBQF
BEDROCK_AGENT_ALIAS_ID=TSTALIASID

# Database Configuration (PostgreSQL for user storage)
DB_HOST=ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech
DB_PORT=5432
DB_NAME=cravin-booking-demo
DB_USERNAME=CravinUAE
DB_PASSWORD=qIuJ6oV3gwzX
DB_SSL=true

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info

PORT=3000