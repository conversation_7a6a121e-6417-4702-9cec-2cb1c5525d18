/**
 * Local development server for Cravin Concierge WhatsApp <PERSON>da
 * Simulates API Gateway for local testing of Lambda functions
 */

import express from 'express';
import dotenv from 'dotenv';
import { handler } from './index.mjs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Parse JSON request bodies
app.use(express.json());

// Middleware to log all requests
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  next();
});

// Transform Express request to Lambda event format
function transformRequestToLambdaEvent(req) {
  return {
    httpMethod: req.method,
    path: req.path,
    queryStringParameters: req.query,
    headers: req.headers,
    body: JSON.stringify(req.body),
    requestContext: {
      http: {
        method: req.method,
        path: req.path
      }
    }
  };
}

// Transform Lambda response to Express response
function sendLambdaResponse(lambdaResponse, res) {
  // Set status code
  res.status(lambdaResponse.statusCode);
  
  // Set headers
  if (lambdaResponse.headers) {
    Object.entries(lambdaResponse.headers).forEach(([key, value]) => {
      res.set(key, value);
    });
  }
  
  // Send body
  if (lambdaResponse.body) {
    try {
      // If body is JSON string, parse it first
      const contentType = lambdaResponse.headers?.['Content-Type'] || '';
      if (contentType.includes('application/json')) {
        res.json(JSON.parse(lambdaResponse.body));
      } else {
        res.send(lambdaResponse.body);
      }
    } catch (error) {
      res.send(lambdaResponse.body);
    }
  } else {
    res.end();
  }
}

// Handle webhook endpoint
app.all('/webhook', async (req, res) => {
  try {
    // Transform request to Lambda event
    const event = transformRequestToLambdaEvent(req);
    
    // Call Lambda handler
    const lambdaResponse = await handler(event);
    
    // Send response
    sendLambdaResponse(lambdaResponse, res);
  } catch (error) {
    console.error('Error handling request:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Handle root path for testing
app.all('/', async (req, res) => {
  try {
    // Transform request to Lambda event
    const event = transformRequestToLambdaEvent(req);

    // Call Lambda handler
    const lambdaResponse = await handler(event);

    // Send response
    sendLambdaResponse(lambdaResponse, res);
  } catch (error) {
    console.error('Error handling request:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`
🚀 Cravin Concierge WhatsApp Lambda Local Server
📡 Server running at http://localhost:${PORT}
🔄 Simulating API Gateway for Lambda functions
🧪 Test WhatsApp webhook verification with:
   GET /?hub.mode=subscribe&hub.verify_token=${process.env.WHATSAPP_VERIFY_TOKEN}&hub.challenge=CHALLENGE_CODE
  
💬 Test WhatsApp message handling with:
   POST / (with WhatsApp message payload)
  
🛑 Press Ctrl+C to stop the server
`);
});
